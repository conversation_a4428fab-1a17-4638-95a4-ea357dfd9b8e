defmodule RepobotWeb.Live.Onboarding.Steps.TemplateRepositoryTest do
  use RepobotWeb.ConnCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  import Phoenix.LiveViewTest
  import Repobot.Test.Fixtures
  import Mox

  alias RepobotWeb.Live.Onboarding.Steps.TemplateRepository

  setup :verify_on_exit!

  describe "background repository loading" do
    test "displays loading state when repositories are being loaded", %{conn: conn} do
      user = user_fixture()
      organization = organization_fixture(user)

      # Mock empty repository cache to trigger background loading
      expect(Repobot.Test.GitHubMock, :user_repos, fn _client, _username ->
        # Simulate slow API response
        Process.sleep(100)
        {:ok, []}
      end)

      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          current_user: user,
          current_organization: organization,
          repositories: [],
          loading_repositories: true,
          loading_progress: 50,
          loading_message: "Fetching repositories from GitHub...",
          selection_mode: :create
        }
      }

      html = render_component(TemplateRepository, socket.assigns)

      assert html =~ "Fetching repositories from GitHub..."
      assert html =~ "animate-spin"
      assert html =~ "width: 50%"
    end

    test "handles repository loading completion successfully" do
      user = user_fixture()
      organization = organization_fixture()
      repository = create_repository(%{organization_id: organization.id})

      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          current_user: user,
          current_organization: organization,
          loading_repositories: true
        }
      }

      # Simulate repository loading completion
      {:ok, updated_socket} =
        TemplateRepository.update(
          %{repository_loading_complete: {:ok, [repository]}},
          socket
        )

      assert updated_socket.assigns.repositories == [repository]
      assert updated_socket.assigns.loading_repositories == false
      assert updated_socket.assigns.loading_progress == 100
    end

    test "handles repository loading failure gracefully" do
      user = user_fixture()
      organization = organization_fixture()

      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          current_user: user,
          current_organization: organization,
          loading_repositories: true
        }
      }

      # Simulate repository loading failure
      {:ok, updated_socket} =
        TemplateRepository.update(
          %{repository_loading_complete: {:error, "API rate limit exceeded"}},
          socket
        )

      assert updated_socket.assigns.loading_repositories == false
      assert updated_socket.assigns.loading_error == "API rate limit exceeded"
    end

    test "updates progress during repository loading" do
      user = user_fixture()
      organization = organization_fixture()

      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          current_user: user,
          current_organization: organization,
          loading_repositories: true,
          loading_progress: 0,
          loading_message: ""
        }
      }

      # Simulate progress update
      {:ok, updated_socket} =
        TemplateRepository.update(
          %{repository_loading_progress: {75, "Processing repositories..."}},
          socket
        )

      assert updated_socket.assigns.loading_progress == 75
      assert updated_socket.assigns.loading_message == "Processing repositories..."
    end

    test "disables select existing option during loading" do
      user = user_fixture()
      organization = organization_fixture()

      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          current_user: user,
          current_organization: organization,
          repositories: [],
          loading_repositories: true,
          loading_progress: 30,
          loading_message: "Loading...",
          selection_mode: :create
        }
      }

      html = render_component(TemplateRepository, socket.assigns)

      assert html =~ "disabled"
      assert html =~ "text-slate-400 cursor-not-allowed"
    end
  end

  describe "fallback to synchronous loading" do
    test "falls back to synchronous loading when Oban job fails to enqueue" do
      user = user_fixture()
      organization = organization_fixture()

      # Create a repository to simulate cached data
      create_repository(%{
        name: "fallback-repo",
        organization_id: organization.id
      })

      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          current_user: user,
          current_organization: organization
        }
      }

      # The step should fall back to synchronous loading if async fails
      # This is tested indirectly by ensuring repositories are loaded
      {:ok, updated_socket} = TemplateRepository.update(%{}, socket)

      # Should have repositories assigned (either from cache or fallback)
      assert Map.has_key?(updated_socket.assigns, :repositories)
    end
  end
end
